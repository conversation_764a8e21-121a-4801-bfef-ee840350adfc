﻿


using System.Collections.ObjectModel;
using DrawnUi.Controls;
using DrawnUi.Views;
using Sandbox.Resources;
using Sandbox.Views.Controls;

namespace Sandbox
{
    public class MainPageCode : BasePageReloadable, IDisposable
    {
        Canvas Canvas;
        SkiaSpinner Spinner;
        SkiaLabel _selectedLabel;
        ObservableCollection<string> _spinnerItems;

        protected override void Dispose(bool isDisposing)
        {
            if (isDisposing)
            {
                this.Content = null;
                Canvas?.Dispose();
            }

            base.Dispose(isDisposing);
        }

        /// <summary>
        /// This will be called by HotReload
        /// </summary>
        public override void Build()
        {
            Canvas?.Dispose();


            Canvas = new Canvas()
            {
                RenderingMode = RenderingModeType.Accelerated,
                Gestures = GesturesMode.Enabled,
                VerticalOptions = LayoutOptions.Fill,
                HorizontalOptions = LayoutOptions.Fill,
                BackgroundColor = Colors.DarkSlateBlue,
                Content =
                    new SkiaStack()
                    {
                        Spacing = 0,
                        VerticalOptions = LayoutOptions.Fill,
                        UseCache = SkiaCacheType.ImageComposite,
                        Children =
                        {
                            new SkiaShape()
                            {
                                HeightRequest = 100,
                                HorizontalOptions = LayoutOptions.Fill,
                                BackgroundColor = Colors.Wheat
                            },
                            new SkiaScroll()
                            {
                                HorizontalOptions = LayoutOptions.Fill,
                                Content = new SkiaWrap()
                                {
                                    UseCache = SkiaCacheType.ImageComposite,
                                    Spacing = 0,
                                    Children =
                                    {


                                        //new SkiaSlider()
                                        //{
                                        //    ControlStyle = PrebuiltControlStyle.Cupertino,
                                        //    End=0.5,
                                        //    Min = 0,
                                        //    Max=3,
                                        //    MinMaxStringFormat="P0",
                                        //    Step=0.01,
                                        //    HorizontalOptions = LayoutOptions.Fill,
                                        //},

                                        new SkiaLayer()
                                        {
                                            HeightRequest=100,
                                            BlockGesturesBelow = true,
                                            Children =
                                            {
                                                new ColorPicker()
                                                {
                                                    HorizontalOptions = LayoutOptions.Fill,
                                                    UseCache = SkiaCacheType.Image,
                                                    WidthRequest = -1
                                                }
                                            }
                                        }

 
                                    }
                                },
                            }
                        }
                    }
            };

            this.Content = Canvas;
        }

        #region TUNE TONE

        private Color _ToneColor = Styles.TintContentColor;
        public Color ToneColor
        {
            get
            {
                return _ToneColor;
            }
            set
            {
                if (_ToneColor != value)
                {
                    _ToneColor = value;
                    OnPropertyChanged();
                    //Debug.WriteLine($"Tint {value}");
                }
            }
        }

        private double _ToneContrast = Styles.TintContentContrast;
        public double ToneContrast
        {
            get
            {
                return _ToneContrast;
            }
            set
            {
                if (_ToneContrast != value)
                {
                    _ToneContrast = value;
                    OnPropertyChanged();
                    //Super.Log($"[VAL1] {value}");
                }
            }
        }

        private double _ToneBrightness = Styles.TintContentLightness;
        public double ToneBrightness
        {
            get
            {
                return _ToneBrightness;
            }
            set
            {
                if (_ToneBrightness != value)
                {
                    _ToneBrightness = value;
                    OnPropertyChanged();
                    //Super.Log($"[VAL2] {value}");
                }
            }
        }


        private double _ToneAlpha = Styles.TintContentColorAlpha;
        public double ToneAlpha
        {
            get
            {
                return _ToneAlpha;
            }
            set
            {
                if (_ToneAlpha != value)
                {
                    _ToneAlpha = value;
                    OnPropertyChanged();
                }
            }
        }

        private double _ToneSaturation = Styles.TintContentSaturation;
        public double ToneSaturation
        {
            get
            {
                return _ToneSaturation;
            }
            set
            {
                if (_ToneSaturation != value)
                {
                    _ToneSaturation = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion

    }
}
