<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="PreviewTests.Views.ScrollPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:PreviewTests.Views.Controls"
    xmlns:demo="clr-namespace:PreviewTests"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:gestures="clr-namespace:AppoMobi.Maui.Gestures;assembly=AppoMobi.Maui.Gestures"
    xmlns:system="clr-namespace:System;assembly=System.Runtime"
    xmlns:views="clr-namespace:PreviewTests.Views"
    x:Name="ThisPage"
    x:DataType="demo:MainPageViewModel">

    <draw:Canvas
        Gestures="Enabled"
        RenderingMode = "Accelerated"
        HorizontalOptions="Fill"
        Tag="MainPage"
        VerticalOptions="Fill">


        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">


            <draw:SkiaScroll
                BackgroundColor="Black"
                HorizontalOptions="Fill"
                Orientation="Both"
                VerticalOptions="Fill"
                ZoomLocked="False"
                ZoomMax="3"
                ZoomMin="1">

                <draw:SkiaLayout
                    BackgroundColor="DimGray"
                    HeightRequest="1500"
                    Tag="Content"
                    UseCache="None"
                    WidthRequest="1500">

                    <draw:SkiaImage
                        HorizontalOptions="Fill"
                        RescalingQuality="None"
                        Source="Images/dungeon.jpg"
                        UseCache="Image"
                        VerticalOptions="Fill" />

                </draw:SkiaLayout>

            </draw:SkiaScroll>

            <draw:SkiaLabelFps
                Margin="0,0,4,24"
                BackgroundColor="DarkRed"
                ForceRefresh="False"
                HorizontalOptions="End"
                Rotation="-45"
                TextColor="White"
                VerticalOptions="End"
                ZIndex="100" />

        </draw:SkiaLayout>

    </draw:Canvas>

</views:BasePageCodeBehind>
