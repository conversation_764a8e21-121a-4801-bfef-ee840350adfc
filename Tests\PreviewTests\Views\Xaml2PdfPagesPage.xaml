<?xml version="1.0" encoding="utf-8" ?>
<views:BasePageCodeBehind
    x:Class="PreviewTests.Views.Xaml2PdfPagesPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:controls="clr-namespace:PreviewTests.Views.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:views="clr-namespace:PreviewTests.Views"
    xmlns:xaml2Pdf="clr-namespace:PreviewTests.Views.Xaml2Pdf"
    BackgroundColor="Gray">

    <Grid
        Padding="0,0,0,8"
        HorizontalOptions="Fill"
        VerticalOptions="FillAndExpand">

        <draw:Canvas
            Margin="8,44"
            BackgroundColor="#66000000"
            Gestures="Enabled"
            RenderingMode = "Accelerated"
            HorizontalOptions="Fill"
            Tag="Warning"
            VerticalOptions="Center">

            <draw:SkiaLayout HorizontalOptions="Fill" VerticalOptions="Fill">

                <draw:SkiaScroll HorizontalOptions="Fill" VerticalOptions="Start">
                    <draw:SkiaLayout HorizontalOptions="Fill">

                        <!--
                            cache ImageComposite will redraw only changed areas
                            so when we press a button only its area will be redrawn
                            over already cached result
                        -->

                        <draw:SkiaLayout
                            Padding="6"
                            BackgroundColor="Blue"
                            HorizontalOptions="Fill"
                            Tag="SkiaStack"
                            Type="Column"
                            UseCache="ImageComposite">

                            <!--  TITLE  -->
                            <draw:SkiaLabel
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                Text="For PDF we will not use the rendered image that you already see below, but we will instantiate the report from scratch."
                                TextColor="Yellow" />

                            <!--  REPORT  -->
                            <xaml2Pdf:ReportSample BackgroundColor="White" HorizontalOptions="Center" />

                            <draw:SkiaLayout
                                ColumnDefinitions="1*,1*"
                                HorizontalOptions="Fill"
                                RowDefinitions="Auto"
                                Type="Grid">

                                <controls:SmallButton
                                    Margin="0,16"
                                    HorizontalOptions="Center"
                                    Tapped="SkiaButton_OnTapped"
                                    Text="Print to custom" />

                                <controls:SmallButton
                                    Grid.Column="1"
                                    Margin="0,16"
                                    HorizontalOptions="Center"
                                    Tapped="SkiaButton_OnTapped2"
                                    Text="Print to A6" />
                            </draw:SkiaLayout>

                        </draw:SkiaLayout>
                    </draw:SkiaLayout>
                </draw:SkiaScroll>

                <!--  FPS  -->
                <draw:SkiaLabelFps
                    Margin="0,0,4,84"
                    BackgroundColor="DarkRed"
                    ForceRefresh="False"
                    HorizontalOptions="End"
                    Rotation="-45"
                    TextColor="White"
                    VerticalOptions="End" />


            </draw:SkiaLayout>
        </draw:Canvas>


    </Grid>

</views:BasePageCodeBehind>
